cmake_minimum_required(VERSION 3.16)
project(VideoService VERSION 1.0.1 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
add_compile_options(-Wall -Wextra -Wpedantic)

# Project directories
set(PROJECT_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(PROJECT_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
set(PROJECT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config)

# Find required packages
find_package(PkgConfig REQUIRED)

# FFmpeg
pkg_check_modules(FFMPEG REQUIRED
    libavformat>=58.0
    libavcodec>=58.0
    libavutil>=56.0
)

# GStreamer
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0>=1.16
    gstreamer-app-1.0
    gstreamer-video-1.0
    gstreamer-rtsp-server-1.0
    gstreamer-allocators-1.0
    gstreamer-sdp-1.0
    gstreamer-webrtc-1.0
)

# JsonCpp
pkg_check_modules(JSONCPP jsoncpp REQUIRED)

# liburing
pkg_check_modules(LIBURING REQUIRED liburing)

# Fast DDS
find_package(fastdds 3 REQUIRED)
find_package(fastcdr 2 REQUIRED)

# OpenCV (optional)
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    add_definitions(-DHAVE_OPENCV)
endif()

# Hardware libraries (optional)
find_library(RGA_LIBRARY NAMES rga PATHS /usr/lib)
find_path(RGA_LIBRARY_INCLUDE_DIRS NAMES rga.h PATHS /usr/include/rga)
if(RGA_LIBRARY AND RGA_LIBRARY_INCLUDE_DIRS)
    set(RGA_FOUND TRUE)
    add_definitions(-DHAVE_RGA)
endif()

find_library(RKMPP_LIBRARY NAMES rockchip_mpp PATHS /usr/lib)
find_path(RKMPP_LIBRARY_INCLUDE_DIRS NAMES rk_mpi.h PATHS /usr/include/rockchip)
if(RKMPP_LIBRARY AND RKMPP_LIBRARY_INCLUDE_DIRS)
    set(RKMPP_FOUND TRUE)
    add_definitions(-DHAVE_RKMPP)
endif()

find_library(RKNN_API_LIBRARY NAMES rknnrt PATHS /usr/lib)
if(RKNN_API_LIBRARY)
    set(RKNNRUNTIME_FOUND TRUE)
    add_definitions(-DHAVE_RKNNRUNTIME)
endif()

# Include directories
include_directories(${PROJECT_INCLUDE_DIR})
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(${GSTREAMER_INCLUDE_DIRS})
include_directories(${JSONCPP_INCLUDE_DIRS})

if(OpenCV_FOUND)
    include_directories(${OpenCV_INCLUDE_DIRS})
endif()

# Link directories
link_directories(${FFMPEG_LIBRARY_DIRS})
link_directories(${GSTREAMER_LIBRARY_DIRS})
link_directories(${JSONCPP_LIBRARY_DIRS})

# DDS subdirectory
add_subdirectory(dds_video_frame)

# Common libraries
set(COMMON_LIBS
    pthread
    dl
    ${FFMPEG_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    ${GSTREAMER_LIBRARIES}
    ${LIBURING_LIBRARIES}
    DDSVideoFrame_lib_${PROJECT_NAME}
    fastdds
    fastcdr
)

# Add OpenCV if found
if(OpenCV_FOUND)
    list(APPEND COMMON_LIBS ${OpenCV_LIBS})
endif()

message(STATUS "Common libs: ${COMMON_LIBS}")

# Hardware libraries (add separately to avoid conflicts)
set(HARDWARE_LIBS)
if(RGA_FOUND)
    list(APPEND HARDWARE_LIBS ${RGA_LIBRARY})
endif()
if(RKMPP_FOUND)
    list(APPEND HARDWARE_LIBS ${RKMPP_LIBRARY})
endif()
if(RKNNRUNTIME_FOUND)
    list(APPEND HARDWARE_LIBS ${RKNN_API_LIBRARY})
endif()

message(STATUS "Hardware libs: ${HARDWARE_LIBS}")

# V4L2 Capture Main
add_executable(v4l2_capture_main
    src/capture/v4l2_capture_main.cpp
    src/transport/video_transport_factory.cpp
)
target_include_directories(v4l2_capture_main PRIVATE ${PROJECT_INCLUDE_DIR})
target_link_libraries(v4l2_capture_main ${COMMON_LIBS})

# RTSP Server Main
add_executable(rtsp_server_main
    src/streaming/rtsp_server_main.cpp
    src/streaming/rtsp_server.cpp
    src/transport/video_transport_factory.cpp
)
target_include_directories(rtsp_server_main PRIVATE ${PROJECT_INCLUDE_DIR})
target_link_libraries(rtsp_server_main ${COMMON_LIBS})

# Cloud Streamer Main
add_executable(cloud_streamer_main
    src/streaming/cloud_streamer_main.cpp
    src/transport/video_transport_factory.cpp
)
target_include_directories(cloud_streamer_main PRIVATE ${PROJECT_INCLUDE_DIR})
target_link_libraries(cloud_streamer_main ${COMMON_LIBS})

add_definitions(-DENABLE_ZERO_COPY)
# Tracker
if (ENABLE_BYTETRACK)
    add_definitions(-DUSE_BYTETRACK)
    add_subdirectory(bytetrack EXCLUDE_FROM_ALL)
endif()
if (ENABLE_BOTSORT)
    add_definitions(-DUSE_BOTSORT)
    add_subdirectory(botsort EXCLUDE_FROM_ALL)
endif()

message(STATUS "RGA include dirs: ${RGA_LIBRARY_INCLUDE_DIRS}")
message(STATUS "RKMPP include dirs: ${RKMPP_LIBRARY_INCLUDE_DIRS}")

# RKNN Track Main
add_executable(rknn_track_main
    src/rknn/rknn_track_main.cpp
    src/rknn/yolo11_model.cpp
    src/transport/video_transport_factory.cpp
)

if (ENABLE_BYTETRACK)
    target_link_libraries(rknn_track_main bytetrack)
    target_include_directories(rknn_track_main PRIVATE ${bytetrack_INCLUDE_DIRS})
endif()
if (ENABLE_BOTSORT)
    target_link_libraries(rknn_track_main botsort)
    target_include_directories(rknn_track_main PRIVATE ${botsort_INCLUDE_DIRS})
endif()

target_include_directories(rknn_track_main PRIVATE
    ${PROJECT_INCLUDE_DIR}
    ${OpenCV_INCLUDE_DIRS}
    ${RGA_LIBRARY_INCLUDE_DIRS}
    runtime/Linux/librknn_api/include)
target_link_libraries(rknn_track_main ${COMMON_LIBS} ${HARDWARE_LIBS})

# Install rknn model and labels.txt to /opt/video_service/models/
install(FILES
    model/yolo11s_rk3576.rknn
    model/labels.txt
    botsort/config/tracker.ini
    botsort/config/gmc.ini
    botsort/config/reid.ini
    DESTINATION /opt/video_service/models/
)

install(FILES
    config/main_v4l2_capture.json
    config/main_rtsp_server.json
    config/main_rknn_track.json
    config/main_cloud_streamer.json
    DESTINATION /opt/video_service/config/
)

install(TARGETS
    v4l2_capture_main
    rtsp_server_main
    cloud_streamer_main
    rknn_track_main
    DESTINATION bin/
)

# Tests
add_executable(video_capture_test
    test/video_capture_test.cpp
    src/transport/video_transport_factory.cpp
)
target_include_directories(video_capture_test PRIVATE ${PROJECT_INCLUDE_DIR})
target_link_libraries(video_capture_test ${COMMON_LIBS})
install(TARGETS
    video_capture_test
    DESTINATION bin/examples/
)

# Configuration summary
message(STATUS "Video Service Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  FFmpeg: ${FFMPEG_FOUND}")
message(STATUS "  GStreamer: ${GSTREAMER_FOUND}")
message(STATUS "  JSONCpp: ${JSONCPP_FOUND}")
message(STATUS "  liburing: ${LIBURING_FOUND}")
message(STATUS "  OpenCV: ${OpenCV_FOUND}")
message(STATUS "  Fast DDS: ${fastdds_FOUND}")
message(STATUS "  Hardware libs: RGA=${RGA_FOUND}, MPP=${RKMPP_FOUND}, RKNN=${RKNNRUNTIME_FOUND}")
