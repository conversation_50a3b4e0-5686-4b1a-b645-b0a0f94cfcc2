#ifndef CLOUD_CONFIG_H__
#define CLOUD_CONFIG_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <cstdint>
#include <string>

// Stream configuration for cloud streaming
struct CloudStreamerConfig {
    enum Type { WEBRTC, RTMP } type = RTMP;
    std::string url;

    // Encoding config
    int bitrate = 2000000;      // 2Mbps
    int gop_size = 15;
    bool use_hw_encoder = true;
    int width = 1280;
    int height = 720;
    int fps = 30;
    std::string codec = "H264";
    std::string preset = "veryfast";
    std::string tune = "zerolatency";
    std::string profile = "baseline";

    // Quality control
    bool adaptive_bitrate = true;
    int min_bitrate = 1000000; // 1Mbps
    int max_bitrate = 4000000; // 4Mbps

    // Transport config
    std::string topic_name = "video_frames";    // DDS input topic name or socket path (for DMA/SHMEM/FASTDDS)
    std::string transport_type = "DMA";        // DMA, SHMEM, FASTDDS
    int domain_id = 0;
    int max_samples = 3;

    // Performance config
    bool low_latency_mode = true;
    int max_queue_size = 30;
};

#ifdef __cplusplus
}
#endif

#endif // CLOUD_CONFIG_H__
