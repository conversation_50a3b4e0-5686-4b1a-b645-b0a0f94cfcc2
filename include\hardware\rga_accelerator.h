#ifndef RGA_ACCELERATOR_H
#define RGA_ACCELERATOR_H

#include "../common.h"

// RGA硬件加速头文件
// 禁用第三方库中的匿名结构体警告（来自im_color结构体定义）
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wpedantic"
#include <im2d.hpp>
#include <RgaUtils.h>
#include <rga.h>
#pragma GCC diagnostic pop

namespace RGAAccelerator {

inline static int v4l2_to_rga_format(int32_t v4l2_format) {
    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV:
            return RK_FORMAT_YUYV_422;
        case V4L2_PIX_FMT_UYVY:
            return RK_FORMAT_UYVY_422;
        case V4L2_PIX_FMT_RGB24:
            return RK_FORMAT_RGB_888;
        case V4L2_PIX_FMT_BGR24:
            return RK_FORMAT_BGR_888;
        case V4L2_PIX_FMT_NV12:
            return RK_FORMAT_YCbCr_420_SP;
        case V4L2_PIX_FMT_NV21:
            return RK_FORMAT_YCrCb_420_SP;
        case V4L2_PIX_FMT_RGBA32:
            return RK_FORMAT_RGBA_8888;
        case V4L2_PIX_FMT_BGRA32:
            return RK_FORMAT_BGRA_8888;
        case V4L2_PIX_FMT_GREY:
            return RK_FORMAT_YCbCr_400;
        default:
            LOG_W("Unsupported V4L2 format for RGA: 0x%08x", v4l2_format);
            return 0;  // 返回0表示不支持的格式
    }
}

inline static int align_width_to_16(int width) {
    // 将宽度对齐到16字节边界
    // 对于不同的像素格式，16字节对应的像素数不同
    return (width + 15) & ~15;
}

inline static int calculate_stride(int width, int32_t format) {
    // 计算stride，确保16字节对齐
    int bytes_per_pixel = 0;

    switch (format) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            bytes_per_pixel = 2;  // 16 bits per pixel
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            bytes_per_pixel = 3;  // 24 bits per pixel
            break;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            bytes_per_pixel = 1;  // 8 bits per pixel for Y plane
            break;
        case V4L2_PIX_FMT_RGBA32:
        case V4L2_PIX_FMT_BGRA32:
            bytes_per_pixel = 4;  // 32 bits per pixel
            break;
        default:
            bytes_per_pixel = 3;  // 默认RGB24
            break;
    }

    // 计算字节宽度并对齐到16字节
    int byte_width = width * bytes_per_pixel;
    int aligned_byte_width = align_width_to_16(byte_width);

    // 返回对齐后的像素宽度
    return aligned_byte_width / bytes_per_pixel;
}

inline static bool color_convert(void* src_addr, int src_width, int src_height, int32_t src_format,
                                void* dst_addr, int32_t dst_format) {
    // 获取RGA格式
    int src_rga_format = v4l2_to_rga_format(src_format);
    int dst_rga_format = v4l2_to_rga_format(dst_format);

    if (src_rga_format == 0 || dst_rga_format == 0) {
        LOG_E("Unsupported format for RGA conversion");
        return false;
    }

    // 计算stride对齐
    int src_stride = calculate_stride(src_width, src_format);
    int dst_stride = calculate_stride(src_width, dst_format);

    // 使用wrapbuffer_virtualaddr_t创建RGA缓冲区
    rga_buffer_t src_buf = wrapbuffer_virtualaddr_t(src_addr,
                                                  src_width, src_height,
                                                  src_stride, src_height,
                                                  src_rga_format);

    rga_buffer_t dst_buf = wrapbuffer_virtualaddr_t(dst_addr,
                                                  src_width, src_height,
                                                  dst_stride, src_height,
                                                  dst_rga_format);

    // 检查和记录stride对齐信息
    LOG_D("RGA operation: src %dx%d (stride %d, format 0x%x) -> dst %dx%d (stride %d, format 0x%x)",
          src_width, src_height, src_stride, src_rga_format,
          src_width, src_height, dst_stride, dst_rga_format);

    int ret = imcvtcolor(src_buf, dst_buf, src_rga_format, dst_rga_format);
    if (ret != IM_STATUS_SUCCESS) {
        LOG_E("RGA format conversion failed: %d", ret);
        return false;
    }
    return true;
}

inline static bool resize(void* src_addr, int src_width, int src_height, int32_t src_format,
                                             void* dst_addr, int dst_width, int dst_height) {

    // 获取RGA格式
    int src_rga_format = v4l2_to_rga_format(src_format);

    if (src_rga_format == 0) {
        LOG_E("Unsupported format for RGA conversion");
        return false;
    }

    // 计算stride对齐
    int src_stride = calculate_stride(src_width, src_format);
    int dst_stride = calculate_stride(dst_width, src_format);

    // 使用wrapbuffer_virtualaddr_t创建RGA缓冲区
    rga_buffer_t src_buf = wrapbuffer_virtualaddr_t(src_addr,
                                                  src_width, src_height,
                                                  src_stride, src_height,
                                                  src_rga_format);

    rga_buffer_t dst_buf = wrapbuffer_virtualaddr_t(dst_addr,
                                                  dst_width, dst_height,
                                                  dst_stride, dst_height,
                                                  src_rga_format);

    // 检查和记录stride对齐信息
    LOG_D("RGA operation: src %dx%d (stride %d, format 0x%x) -> dst %dx%d (stride %d, format 0x%x)",
          src_width, src_height, src_stride, src_rga_format,
          dst_width, dst_height, dst_stride, src_rga_format);

    // 执行RGA操作：格式转换 + 缩放
    int ret = imresize(src_buf, dst_buf);
    if (ret != IM_STATUS_SUCCESS) {
        LOG_E("RGA resize operation failed: %d", ret);
        return false;
    }

    return true;
}

inline static bool crop_and_resize(void* src_addr, int src_width, int src_height, int32_t src_format,
                                           int crop_x, int crop_y, int crop_w, int crop_h,
                                           void* dst_addr, int dst_width, int dst_height) {
    // 获取RGA格式
    int rga_format = v4l2_to_rga_format(src_format);
    if (rga_format == 0) {
        LOG_E("Unsupported format for RGA crop and resize");
        return false;
    }

    // 计算stride对齐
    int src_stride = calculate_stride(src_width, src_format);
    int dst_stride = calculate_stride(dst_width, src_format);

    // 使用wrapbuffer_virtualaddr_t创建RGA缓冲区
    rga_buffer_t src_buf = wrapbuffer_virtualaddr_t(src_addr,
                                                  src_width, src_height,
                                                  src_stride, src_height,
                                                  rga_format);

    rga_buffer_t dst_buf = wrapbuffer_virtualaddr_t(dst_addr,
                                                  dst_width, dst_height,
                                                  dst_stride, dst_height,
                                                  rga_format);

    // 设置裁剪区域
    im_rect src_rect = {crop_x, crop_y, crop_w, crop_h};
    im_rect dst_rect = {0, 0, dst_width, dst_height};

    LOG_D("RGA crop and resize: src %dx%d crop(%d,%d,%d,%d) -> dst %dx%d",
          src_width, src_height, crop_x, crop_y, crop_w, crop_h, dst_width, dst_height);

    // 执行RGA操作：裁剪 + 缩放
    int ret = improcess(src_buf, dst_buf, {}, src_rect, dst_rect, {}, 0);
    if (ret != IM_STATUS_SUCCESS) {
        LOG_E("RGA crop and resize operation failed: %d", ret);
        return false;
    }

    return true;
}

}

#endif // RGA_ACCELERATOR_H
